function foo(){return {encoding:function(){var a="\xa0 => \xa0";console.log("foo")},encoding2:function(){console.log("𠮷"==="𠮷");console.log("𠮷"==="\\u{20BB7}");console.log("𠮷"==="\\𠮷");console.log(`𠮷`===`𠮷`);console.log(`${"𠮷"}`==="𠮷");console.log(`prefix-${"𠮷"}`===`prefix-𠮷`);console.log(`${"𠮷"}-suffix`===`𠮷-suffix`);console.log("𠮷"==="prefix-\\𠮷");console.log("𠮷"==="𠮷-suffix");console.log("𠮷"==="\\u{20BB7}-suffix");console.log("𠮷"==="\\𠮷-suffix")}}}