/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.sources;

import com.sencha.command.environment.BuildEnvironment;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.ScriptTestBase;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.RootNode;
import com.sencha.tools.compiler.builder.SimpleConcatenator;
import com.sencha.tools.compiler.builder.optimizer.Optimizer;
import com.sencha.tools.compressors.JsLanguageLevel;
import com.sencha.tools.compressors.closure.ClosureCompressor;
import com.sencha.tools.compressors.es6.CmdJavascriptCompressor;
import com.sencha.util.*;
import org.junit.Test;

import java.io.File;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.assertEquals;

public class ES6Test extends ScriptTestBase {


    @Test
    public void testEs6() {
        String _inputFile = "ES6Test_Input.js";
        String _expectedFile = "ES6Test_Expected.js";
        String _expectedMinFile = "ES6Test_Expected_Minified.js";

        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        File expectedMinFile = new File(getPath(_expectedMinFile, 2));

        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);
        
        String expectedMin = FileUtil.readFile(expectedMinFile);
        m = p.matcher(expectedMin);
        expectedMin = m.replaceAll("");
        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");
        assertEquals(expectedMin, content);
    }

    @Test
    public void testEs6_2() {
        String _inputFile = "ES6Test_Input2.js";
        String _expectedFile = "ES6Test_Expected2.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        BuildEnvironment be = BuildEnvironment.load(null, ".");
        String content = AstUtil.toSource(node, be);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);


        CmdJavascriptCompressor compressor = new CmdJavascriptCompressor();
        compressor.setOutputLanguageLevel(JsLanguageLevel.ANY);
        compressor.setInputLanguageLevel(JsLanguageLevel.ANY);
        compressor.setBuildEnvironment(be);
        String compressed = compressor.compress(content);
        String compressedFile = "ES6Test_Expected_Minified2.js";
        File expectedMinFile = new File(getPath(compressedFile, 2));
        String expectedMin = FileUtil.readFile(expectedMinFile);
        assertEquals(expectedMin, compressed);
    }

    @Test
    public void testEs6_8to5() {
        String _inputFile = "ES6Test_Input3.js";
        String _expectedFile = "ES6Test_Expected3.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        BuildEnvironment be = BuildEnvironment.load(null, ".");
        String content = AstUtil.toSource(node, be);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);


        CmdJavascriptCompressor compressor = new CmdJavascriptCompressor();
        compressor.setInputLanguageLevel(JsLanguageLevel.ES8);
        compressor.setOutputLanguageLevel(JsLanguageLevel.ES5);
        compressor.setBuildEnvironment(be);
        compressor.setOptions(new HashMap<String, Object>(){{
            put("polyfills", "auto");
        }});
        String compressed = compressor.compress(content);
        compressed = AstUtil.toSource(AstUtil.parse(compressed), true);
        String compressedFile = "ES6Test_Expected_Minified3.js";
        File expectedMinFile = new File(getPath(compressedFile, 2));
        String expectedMin = FileUtil.readFile(expectedMinFile);
        assertEquals(expectedMin, compressed);
    }

    @Test
    public void testEs6_8to6() {
        String _inputFile = "ES6Test_Input3.js";
        String _expectedFile = "ES6Test_Expected3.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        BuildEnvironment be = BuildEnvironment.load(null, ".");
        String content = AstUtil.toSource(node, be);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);


        CmdJavascriptCompressor compressor = new CmdJavascriptCompressor();
        compressor.setInputLanguageLevel(JsLanguageLevel.ES8);
        compressor.setOutputLanguageLevel(JsLanguageLevel.ES6);
        compressor.setBuildEnvironment(be);
        compressor.setOptions(new HashMap<String, Object>(){{
            put("polyfills", "auto");
        }});
        String compressed = compressor.compress(content);
        compressed = AstUtil.toSource(AstUtil.parse(compressed), true);
        // TODO: eventually closure may support transforming the async / await 
        // calls to es6 w/o needing to use the es5 transforms
        String compressedFile = "ES6Test_Expected_Minified4.js";
        File expectedMinFile = new File(getPath(compressedFile, 2));
        String expectedMin = FileUtil.readFile(expectedMinFile);
        assertEquals(expectedMin, compressed);
    }

    @Test
    public void testEs6Transpile() {
        String _inputFile = "ES6Test_Transpile_Input.js";
        String _expectedFile = "ES6Test_Transpile_Expected.js";
        String _expectedTranspiledFile = "ES6Test_Transpile_Expected_Transpiled.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        File expectedTranspiledFile = new File(getPath(_expectedTranspiledFile, 2));

        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        String expectedTranspiled = FileUtil.readFile(expectedTranspiledFile);
        m = p.matcher(expectedTranspiled);
        expectedTranspiled = m.replaceAll("");

        ClosureCompressor comp = new ClosureCompressor();
        comp.setOptions(new HashMap<String, Object>(){{
            put("prettyPrint", true);
            put("collapseVariableDeclarations", true);
            put("quoteKeywordProperties", true);
        }});
        comp.setOutputLanguageLevel(JsLanguageLevel.ES3);
        comp.setTranspileOnly(true);
        String compressed = comp.compress(content);
        assertEquals(
                StringUtil.normalizeLineEndings(expectedTranspiled),
                StringUtil.normalizeLineEndings(compressed));
    }

    @Test
    public void testEs6TranspileNoPolyfill() {
        String _inputFile = "ES6Test_Transpile_NoPolyfill_Input.js";
        String _expectedFile = "ES6Test_Transpile_NoPolyfill_Expected.js";
        String _expectedTranspiledFile = "ES6Test_Transpile_NoPolyfill_Expected_Transpiled.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        File expectedTranspiledFile = new File(getPath(_expectedTranspiledFile, 2));

        RootNode node = AstUtil.parseClosure(FileUtil.readFile(inputFile));
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        String expectedTranspiled = FileUtil.readFile(expectedTranspiledFile);
        m = p.matcher(expectedTranspiled);
        expectedTranspiled = m.replaceAll("");

        CmdJavascriptCompressor comp = new CmdJavascriptCompressor();
        comp.setOptions(new HashMap<String, Object>(){{
            put("prettyPrint", true);
            put("collapseVariableDeclarations", true);
            put("quoteKeywordProperties", true);
            put("polyfills", "used");
        }});
        comp.setOutputLanguageLevel(JsLanguageLevel.ES3);
        String compressed = comp.compress(content);
        compressed = AstUtil.toSource(AstUtil.parse(compressed), true);
        m = p.matcher(compressed);
        compressed = m.replaceAll("");
        assertEquals(
                StringUtil.normalizeLineEndings(expectedTranspiled),
                StringUtil.normalizeLineEndings(compressed));
    }

    @Test
    public void testExtEs6() {
        String _inputFile = "Ext_ES6_Input.js";
        String _expectedFile = "Ext_ES6_Expected.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));

        CompilerContext context = new CompilerContext(new Configuration());

        ClassPathScope scope = context.createClassPathScope(inputFile.getAbsolutePath());
        scope.includeAll();


        Optimizer opt = new Optimizer();
        opt.setNameOptimization(true);
        opt.setOptimizeParentCalls(true);
        opt.doBuild(context);

        SimpleConcatenator concat = new SimpleConcatenator();
        StringBuilder buff = new StringBuilder();
        StringBuilderWriter writer = new StringBuilderWriter(buff);
        concat.doConcat(context, writer);

        String actual = writer.getBuilder().toString();
        String expected = FileUtil.readFile(expectedFile);

        actual = StringUtil.normalizeLineEndings(actual);
        expected = StringUtil.normalizeLineEndings(expected);

        assertEquals(expected, actual);
    }

}
