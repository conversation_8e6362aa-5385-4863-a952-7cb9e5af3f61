var one = 1,
    two = 2,
    three = 3,
    f = foo;
function globalFunc(b, a, c) {
    console.log(one, two, three, b, a, c);
}
function g() {}
var four = {
        theTestCase: function() {
            function a() {
                var a, c;
                function b() {
                    var b, d;
                    function c() {
                        var c = 41;
                        a = 42 + b;
                    }
                }
            }
        },
        prop: function(d, b, a) {
            var c = 9,
                e = 10;
            console.log(one, two, three, d, b, a, c, e);
        },
        anotherProp: function(b, a, c) {
            var f = 10;
            var e = 100,
                d = 200;
            console.log(e, d, three, six, b, a, c, f);
            if (true) {
                let e = 1000,
                    f = 2;
                console.log(e, d, three, b, a, c, f);
            }
        },
        foo(arg1, arg2) {
            var var1 = "hello",
                result = function(arg3) {
                    eval("arg3 = 'world';");
                    return arg3;
                };
            console.log(var1, result, arg1, arg2);
        },
        bar(b, a) {
            var {baz:[] bip, foo: { baz: bar} } = this.foo(b, a),
                [first, second, , third] = [
                    1,
                    2,
                    3,
                    4,
                    5
                ];
            console.log(bip, bar, first, second, third);
        },
        func() {
            var a = this;
            var b = a.performance && a.performance.now ? function() {} : Ext.now;
        },
        func2() {
            var b = Array.prototype,
                a = b.slice,
                f = (function() {
                    var a = [],
                        b,
                        c = 20;
                    if (!a.splice) {
                        return false;
                    }
                    // This detects a bug in IE8 splice method:
                    // see http://social.msdn.microsoft.com/Forums/en-US/iewebdevelopment/thread/6e946d03-e09f-4b22-a4dd-cd5e276bf05a/
                    while (c--) {
                        a.push("A");
                    }
                    a.splice(15, 0, "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F");
                    b = a.length;
                    //41
                    a.splice(13, 0, "XXX");
                    // add one element
                    if (b + 1 !== a.length) {
                        return false;
                    }
                    // end IE8 bug
                    return true;
                }()),
                e = 'indexOf' in b,
                d = true;
            var g = {
                    slice: ([
                        1,
                        2
                    ].slice(1, undefined).length ? function(b, c, d) {
                        return a.call(b, c, d);
                    } : function(b, c, d) {
                        // see http://jsperf.com/slice-fix
                        if (typeof c === 'undefined') {
                            return a.call(b);
                        }
                        if (typeof d === 'undefined') {
                            return a.call(b, c);
                        }
                        return a.call(b, c, d);
                    })
                };
            var c;
            Ext.Version = c = function(a, g) {
                var f = this,
                    c = f.padModes,
                    e, k, d, j, i, h, b;
                if (a.isVersion) {
                    a = a.version;
                }
                f.version = b = String(a).toLowerCase().replace(underscoreRe, '.').replace(plusMinusRe, '');
                e = b.charAt(0);
                if (e in c) {
                    b = b.substring(1);
                    d = c[e];
                } else {
                    d = g ? c[g] : 0;
                }
                // careful - NaN is falsey!
                f.pad = d;
            };
        },
        asdfasdf: function(a) {
            var b = 1,
                a;
            console.log(a);
        },
        someFunction() {
            var a = "world",
                b = function() {
                    var b = (a || (a = "hello")).length,
                        c;
                    console.log(b);
                };
        },
        testFnName() {
            var a = function() {
                    function a() {
                        console.log("the funcName fn");
                    }
                    return a();
                };
        },
        testCatchBlock() {
            function a(h, i, a, j) {
                var k = this,
                    n = xds.component.Definition.aliasNamepool,
                    e = i.cn,
                    d = k.getModelFromSnapshot(i),
                    b = d.getInstance(),
                    l = b.getUserAlias(),
                    c, g;
                if (a) {
                    a = a.getInstance();
                    // We cannot accept the root instance as a parent argument, because
                    // later in this function we determine validity checks based on the
                    // presence of the parentInstance argument
                    if (a === h.getRootNode().getInstance()) {
                        a = null;
                    }
                } else {
                    b.validAsTopInstance = true;
                }
                // Validate the instance can go under parent
                if (!b.$isValidParent(a || null)) {
                    return null;
                }
                if (a) {
                    if (!a.$isValidChild(b)) {
                        return null;
                    }
                }
                if (a) {
                    if (a.$onBeforeRestoreChild(b) === false) {
                        return null;
                    }
                    c = a.getModel();
                } else {
                    c = h.getRootNode();
                }
                if (b.$onBeforeRestore(a) === false) {
                    return null;
                }
                if (j) {
                    g = j.getModel();
                }
                if (b.name) {
                    xds.component.Definition.addToNamePool(b.name);
                }
                xds.component.Definition.addToAliasNamePool(l, i.type);
                // we don't want add/addchild/etc. events firing as a result of model insertion
                if (a) {
                    a.suspendEvents();
                }
                b.suspendEvents();
                if (g) {
                    c.insertBefore(d, g);
                } else {
                    c.appendChild(d);
                }
                if (a) {
                    a.resumeEvents();
                    a.$onRestoreChild(b);
                }
                b.resumeEvents();
                if (e) {
                    for (var f = 0,
                        m = e.length; f < m; f++) {
                        try {
                            k.restoreTo(h, e[f], d);
                        } catch (o) {
                            xds.ui.Ack.display('Unsupported sub component type: ' + e[f].type);
                        }
                    }
                }
                b.$onRestore(a);
                return b;
            }
        },
        methodWithDefaults: function(foo = "bar") {
            console.log(foo);
        },
        methodWithDefaultsNew: function(foo = new ArrayBuffer(24.0)) {
            console.log(foo);
        },
        newOperators: function() {
            var a = function() {},
                b = new a();
            function c(a) {
                return new a();
            }
            function d(b) {
                function c() {
                    return new a(new b(), new ArrayBuffer());
                }
            }
            return b;
        },
        propagate: function(c, a, b, d) {
            if (Ext.isFunction(a)) {
                this.schedule(function() {
                    try {
                        b.resolve(a(c));
                    } catch (e) {
                        b.reject(e);
                    }
                });
            } else {
                d.call(this.deferred, c);
            }
        },
        testScope: function() {
            function a() {}

            var h = this,
                j = a(),
                c = 3,
                e = 4,
                d = 5,
                i = f,
                b = g();
        },
        testNestedScope: function() {
            function a() {
                function a() {}

                var h = this,
                    j = a(),
                    c = 3,
                    e = 4,
                    d = 5,
                    i = f,
                    b = g();
            }

            var h = this,
                j = a(),
                c = 3,
                e = 4,
                d = 5,
                i = 6,
                b = 7;
        },
        testDoIf: function() {
            do {
                console.log("stuff");
            } while (false);
            do if (true)  {
                console.log('foo');
            }
             while (false);
        },
        testLabels: function() {
            var a = 1,
                b = 2;
            c: while (true) {
                d: while (true) {
                    a = b + a;
                    break d;
                }
                break c;
            }
        }
    };
