import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.RootNode;

public class TestDestructuring {
    public static void main(String[] args) {
        String[] testCases = {
            "var {baz: bip, foo: { baz: bar} } = this.foo(arg4, arg2);",
            "var [a, b, c] = [1, 2, 3];",
            "var {baz: [bip], foo: { baz: bar} } = this.foo(arg4, arg2);"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            System.out.println("\n=== Test Case " + (i + 1) + " ===");
            System.out.println("Input: " + testCase);
            
            try {
                RootNode root = AstUtil.parseClosure(testCase, "test-case-" + (i + 1) + ".js");
                System.out.println("✓ Parsing successful!");
                System.out.println("Output: " + root.toSource());
            } catch (Exception e) {
                System.err.println("✗ Error: " + e.getMessage());
                if (e.getCause() != null) {
                    System.err.println("Cause: " + e.getCause().getMessage());
                }
            }
        }
    }
}
