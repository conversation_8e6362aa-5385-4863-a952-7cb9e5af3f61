<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="App Upgrade Tests" type="JUnit" factoryName="JUnit" singleton="true">
    <module name="sencha-test-harness" />
    <option name="PACKAGE_NAME" value="com.sencha.upgrades" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="package" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>