<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="runLocalTestsNoArchive" type="JUnit" factoryName="JUnit">
    <module name="sencha-command-test" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sencha.integration.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <option name="PACKAGE_NAME" value="com.sencha.tools.test.server" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.tools.test.server.JobServerTest" />
    <option name="METHOD_NAME" value="runLocalTestsNoArchive" />
    <option name="TEST_OBJECT" value="method" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$/build/" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>