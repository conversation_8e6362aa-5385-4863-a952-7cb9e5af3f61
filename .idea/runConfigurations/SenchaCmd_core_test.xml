<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SenchaCmd core test" type="JUnit" factoryName="JUnit" singleton="true">
    <module name="sencha-command" />
    <option name="PACKAGE_NAME" value="com.sencha" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="directory" />
    <option name="VM_PARAMETERS" value="-ea -Xms256m -Xmx1024m" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$/build/test-temp" />
    <dir value="$PROJECT_DIR$/sencha-command/test" />
    <RunnerSettings RunnerId="Debug">
      <option name="DEBUG_PORT" value="" />
      <option name="TRANSPORT" value="0" />
      <option name="LOCAL" value="true" />
    </RunnerSettings>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Debug" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>