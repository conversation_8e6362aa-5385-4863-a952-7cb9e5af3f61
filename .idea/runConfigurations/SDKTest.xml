<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SDKTest" type="JUnit" factoryName="JUnit" singleton="true" nameIsGenerated="true">
    <module name="sencha-test-harness" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sencha.integration.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <option name="PACKAGE_NAME" value="com.sencha.integration" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.integration.SDKTest" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea -Xms512m -Xmx2048m" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <RunnerSettings RunnerId="Debug">
      <option name="DEBUG_PORT" value="" />
      <option name="TRANSPORT" value="0" />
      <option name="LOCAL" value="true" />
    </RunnerSettings>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Debug" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>