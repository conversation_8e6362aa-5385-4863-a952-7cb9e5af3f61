<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="CompilerContextIntegrationTest" type="JUnit" factoryName="JUnit" singleton="true" nameIsGenerated="true">
    <module name="sencha-test-harness" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sencha.tools.compiler.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <option name="PACKAGE_NAME" value="com.sencha.tools.compiler" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.tools.compiler.CompilerContextIntegrationTest" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$/build/test-temp" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>