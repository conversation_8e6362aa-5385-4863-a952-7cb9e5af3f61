<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SenchaCmd fashion test" type="JUnit" factoryName="JUnit">
    <module name="sencha-fashion" />
    <option name="PACKAGE_NAME" value="" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="directory" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <dir value="$PROJECT_DIR$/sencha-fashion/test" />
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>