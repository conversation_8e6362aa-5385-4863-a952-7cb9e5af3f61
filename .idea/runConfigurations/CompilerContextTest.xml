<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="CompilerContextTest" type="JUnit" factoryName="JUnit" singleton="true" nameIsGenerated="true">
    <module name="sencha-command" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sencha.tools.compiler.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <option name="PACKAGE_NAME" value="com.sencha.tools.compiler" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.tools.compiler.CompilerContextTest" />
    <option name="METHOD_NAME" value="testCircularReferenceDetection" />
    <option name="TEST_OBJECT" value="class" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$/build/test-temp" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <RunnerSettings RunnerId="Debug">
      <option name="DEBUG_PORT" value="" />
      <option name="TRANSPORT" value="0" />
      <option name="LOCAL" value="true" />
    </RunnerSettings>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Debug" />
    <ConfigurationWrapper RunnerId="Run" />
    <method v="2" />
  </configuration>
</component>